{"name": "Postcode Flow_AU_ANDROID", "created": "2025-07-02 23:54:20", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "H9fy9qcFbZ", "executionTime": "1586ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"action_id": "Y8vz7AJD1i", "executionTime": "2289ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "J9loj6Zl5K", "executionTime": "441ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "xz8njynjpZ", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250702071621.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": 1745665879530, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": 1749384094911, "type": "text"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@5", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 5, "timestamp": 1751405478055, "type": "multiStep"}, {"action_id": "RLz6vQo3ag", "executionTime": "2270ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "method": "locator", "timeout": 10, "timestamp": 1751404760227, "type": "waitTill"}, {"action_id": "QMXBlswP6H", "double_tap": false, "executionTime": "4476ms", "image_filename": "homepage_editbtn-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746143899898, "type": "tapOnText"}, {"action_id": "pldheRUBVi", "executionTime": "1022ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751442053790, "type": "tap"}, {"action_id": "kbdEPCPYod", "delay": 1000, "executionTime": "1364ms", "text": "5000", "timestamp": 1746144035427, "type": "textClear"}, {"action_id": "pldheRUBVi", "executionTime": "917ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751451132525, "type": "tap"}, {"action_id": "mw9GQ4mzRE", "double_tap": false, "executionTime": "1821ms", "image_filename": "HAYMARKET-SE.png", "method": "image", "text_to_find": "BC", "threshold": 0.7, "timeout": 30, "timestamp": 1746144235322, "type": "tapOnText"}, {"action_id": "kDnmoQJG4o", "executionTime": "215ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"btnSaveOrContinue\"]", "timeout": 10, "timestamp": 1751443203144, "type": "waitTill"}, {"action_id": "E2jpN7BioW", "double_tap": false, "executionTime": "21227ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"btnSaveOrContinue\"]", "method": "locator", "text_to_find": "Save", "timeout": 10, "timestamp": 1746144262142, "type": "tap"}, {"action_id": "70iOOakiG7", "double_tap": false, "executionTime": "2340ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "IupxLP2Jsr", "enter": true, "executionTime": "280ms", "function_name": "text", "text": "P_6225544", "timestamp": 1745484826180, "type": "text"}, {"action_id": "BQ7Cxm53HQ", "executionTime": "17567ms", "interval": 0.5, "locator_type": "text", "locator_value": "UNO", "timeout": 30, "timestamp": 1751443286537, "type": "waitTill"}, {"action_id": "VkUKQbf1Qt", "double_tap": false, "executionTime": "4060ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746144823467, "type": "tapOnText"}, {"action_id": "YhLhTn3Wtm", "duration": 5, "time": 5, "timestamp": 1751457606479, "type": "wait", "executionTime": "5056ms"}, {"action_id": "pldheRUBVi", "executionTime": "17759ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751442898549, "type": "tap"}, {"action_id": "kbdEPCPYod", "delay": 1000, "executionTime": "1418ms", "text": "4000", "timestamp": 1751442824674, "type": "textClear"}, {"action_id": "pldheRUBVi", "executionTime": "273ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751451263586, "type": "tap"}, {"action_id": "VkUKQbf1Qt", "double_tap": false, "executionTime": "17752ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "CITY", "threshold": 0.7, "timeout": 30, "timestamp": 1751443446880, "type": "tapOnText"}, {"action_id": "IOc0IwmLPQ", "executionTime": "217ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"btnSaveOrContinue\"]", "timeout": 10, "timestamp": 1751443414780, "type": "waitTill"}, {"action_id": "E2jpN7BioW", "double_tap": false, "executionTime": "782ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"btnSaveOrContinue\"]", "method": "locator", "text_to_find": "Save", "timeout": 10, "timestamp": 1751442926614, "type": "tap"}, {"action_id": "73NABkfWyY", "executionTime": "4998ms", "locator_type": "text", "locator_value": "<PERSON><PERSON><PERSON>", "timeout": 10, "timestamp": 1746145022497, "type": "exists"}, {"action_id": "VkUKQbf1Qt", "double_tap": false, "executionTime": "3863ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "UNO", "threshold": 0.7, "timeout": 30, "timestamp": 1751444026038, "type": "tapOnText"}, {"action_id": "BQ7Cxm53HQ", "executionTime": "2384ms", "interval": 0.5, "locator_type": "text", "locator_value": "UNO", "timeout": 30, "timestamp": 1751444094050, "type": "waitTill"}, {"action_id": "lnjoz8hHUU", "double_tap": false, "executionTime": "5049ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746145090019, "type": "tapOnText"}, {"action_id": "WmNWcsWVHv", "double_tap": false, "executionTime": "1936ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Search suburb or postcode\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Search suburb or postcode", "method": "locator", "text_to_find": "4000", "timeout": 30, "timestamp": 1746145246389, "type": "tapOnText"}, {"action_id": "uZHvvAzVfx", "delay": 500, "executionTime": "901ms", "text": "HAYMARKET", "timestamp": 1746145223768, "type": "textClear"}, {"action_id": "pldheRUBVi", "executionTime": "957ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "timestamp": 1751444225167, "type": "tap"}, {"action_id": "H0ODFz7sWJ", "double_tap": false, "executionTime": "14136ms", "image_filename": "HAYMARKET-SE.png", "method": "image", "text_to_find": "2000", "threshold": 0.7, "timeout": 30, "timestamp": 1746145274227, "type": "tapOnText"}, {"action_id": "IOc0IwmLPQ", "executionTime": "215ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"btnSaveOrContinue\"]", "timeout": 10, "timestamp": 1751444259985, "type": "waitTill"}, {"action_id": "E2jpN7BioW", "double_tap": false, "executionTime": "750ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"btnSaveOrContinue\"]", "method": "locator", "text_to_find": "Save", "timeout": 10, "timestamp": 1751444276258, "type": "tap"}, {"action_id": "eRCmRhc3re", "executionTime": "1157ms", "locator_type": "text", "locator_value": "Broadway", "timeout": 20, "timestamp": 1746145322752, "type": "exists"}, {"action_id": "DfwaiVZ8Z9", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 50, "executionTime": "1395ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1751444331993, "type": "swipe", "vector_end": [0.5, 0.5], "vector_start": [0.5, 0.7]}, {"action_id": "94ikwhIEE2", "double_tap": false, "executionTime": "5103ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "text_to_find": "bag", "threshold": 0.7, "timeout": 30, "timestamp": 1746145693144, "type": "tapOnText"}, {"action_id": "rkwVoJGZG4", "executionTime": "343ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1751444508689, "type": "tap"}, {"action_id": "qofJDqXBME", "executionTime": "4816ms", "interval": 0.5, "locator_type": "text", "locator_value": "Delivery", "timeout": 10, "timestamp": 1751451610048, "type": "waitTill"}, {"action_id": "3gJsiap2Ds", "double_tap": false, "executionTime": "26290ms", "image_filename": "cnc-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Click & Collect\"]", "method": "locator", "text_to_find": "Collect", "threshold": 0.7, "timeout": 30, "timestamp": 1746169580270, "type": "tapOnText"}, {"action_id": "G4A3KBlXHq", "executionTime": "1881ms", "text_to_find": "Nearby", "timeout": 30, "timestamp": 1746145831229, "type": "tapOnText"}, {"action_id": "QpBLC6BStn", "delay": 500, "executionTime": "871ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "delete", "method": "locator", "text": "3000", "timeout": 10, "timestamp": 1746145953013, "type": "textClear"}, {"action_id": "ZWpYNcpbFA", "double_tap": false, "executionTime": "23740ms", "image_filename": "MELBOURNE_SE.png", "method": "image", "text_to_find": "VIC", "threshold": 0.7, "timeout": 30, "timestamp": 1746146079046, "type": "tapOnText"}, {"action_id": "GYK47u1y3A", "executionTime": "314ms", "function_name": "send_key_event", "key_event": "TAB", "timestamp": 1751455570517, "type": "androidFunctions"}, {"action_id": "s8h8VDUIOC", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "1409ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Remove UNO Card Game", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746146260557, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "eVytJrry9x", "executionTime": "407ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1746146287245, "type": "tap"}, {"action_id": "Tebej51pT2", "executionTime": "364ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1746146348304, "type": "tap"}, {"action_id": "0f2FSZYjWq", "executionTime": "4978ms", "locator_type": "text", "locator_value": "3000", "timeout": 10, "timestamp": 1746146563188, "type": "exists"}, {"action_id": "rkwVoJGZG4", "executionTime": "336ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1751445434641, "type": "tap"}, {"action_id": "mWeLQtXiL6", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3253ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746146644650, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "xyHVihJMBi", "executionTime": "1446ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746146665049, "type": "tap"}], "labels": [], "updated": "2025-07-02 23:54:20"}