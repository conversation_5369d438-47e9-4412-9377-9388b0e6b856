2025-07-03 06:24:50,981 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-03 06:24:50,982 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-03 06:24:50,982 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-03 06:24:50,982 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-03 06:24:50,983 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-03 06:24:50,983 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-03 06:24:50,983 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-03 06:24:50,983 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-03 06:24:50,984 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-03 06:24:50,984 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-03 06:24:50,984 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-03 06:24:50,985 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-07-03 06:24:50,985 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-07-03 06:24:53,036 - __main__ - INFO - Existing processes terminated
2025-07-03 06:24:53,993 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
2025-07-03 06:24:53,994 - utils.global_values_db - INFO - Global values database initialized successfully
2025-07-03 06:24:53,994 - utils.global_values_db - INFO - Using global values from config.py
2025-07-03 06:24:53,994 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-03 06:24:53,997 - utils.global_values_db - INFO - Global value saved: Connection Retry Attempts=3 (type: int)
2025-07-03 06:24:53,997 - utils.global_values_db - INFO - Initialized default value: Connection Retry Attempts=3
2025-07-03 06:24:53,998 - utils.global_values_db - INFO - Global value saved: Connection Retry Delay=2 (type: int)
2025-07-03 06:24:53,998 - utils.global_values_db - INFO - Initialized default value: Connection Retry Delay=2
2025-07-03 06:24:53,999 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-03 06:24:53,999 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-03 06:24:54,030 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-03 06:24:54,443 - app - INFO - Using directories from config.py:
2025-07-03 06:24:54,443 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-03 06:24:54,443 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-03 06:24:54,443 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-03 06:24:54,486] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-03 06:24:54,486] INFO in database: Test_steps table schema updated successfully
[2025-07-03 06:24:54,486] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-03 06:24:54,487] INFO in database: Screenshots table schema updated successfully
[2025-07-03 06:24:54,487] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-03 06:24:54,488] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-03 06:24:54,488] INFO in database: action_type column already exists in execution_tracking table
[2025-07-03 06:24:54,488] INFO in database: action_params column already exists in execution_tracking table
[2025-07-03 06:24:54,488] INFO in database: action_id column already exists in execution_tracking table
[2025-07-03 06:24:54,488] INFO in database: Successfully updated execution_tracking table schema
[2025-07-03 06:24:54,488] INFO in database: Database initialized successfully
[2025-07-03 06:24:54,488] INFO in database: Checking initial database state...
[2025-07-03 06:24:54,507] INFO in database: Database state: 0 suites, 0 cases, 9616 steps, 1 screenshots, 160 tracking entries
[2025-07-03 06:24:54,508] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-03 06:24:54,509] INFO in database: Test_steps table schema updated successfully
[2025-07-03 06:24:54,509] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-03 06:24:54,509] INFO in database: Screenshots table schema updated successfully
[2025-07-03 06:24:54,509] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-03 06:24:54,510] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-03 06:24:54,510] INFO in database: action_type column already exists in execution_tracking table
[2025-07-03 06:24:54,510] INFO in database: action_params column already exists in execution_tracking table
[2025-07-03 06:24:54,510] INFO in database: action_id column already exists in execution_tracking table
[2025-07-03 06:24:54,510] INFO in database: Successfully updated execution_tracking table schema
[2025-07-03 06:24:54,510] INFO in database: Database initialized successfully
[2025-07-03 06:24:54,510] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-03 06:24:54,510] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-03 06:24:54,510] INFO in database: action_type column already exists in execution_tracking table
[2025-07-03 06:24:54,511] INFO in database: action_params column already exists in execution_tracking table
[2025-07-03 06:24:54,511] INFO in database: action_id column already exists in execution_tracking table
[2025-07-03 06:24:54,511] INFO in database: Successfully updated execution_tracking table schema
[2025-07-03 06:24:54,511] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-03 06:24:54,511] INFO in database: Screenshots table schema updated successfully
[2025-07-03 06:24:54,511] INFO in database: === CLEARING EXECUTION TRACKING TABLE ===
[2025-07-03 06:24:54,512] INFO in database: Found 160 records in execution_tracking table before clearing
[2025-07-03 06:24:54,513] INFO in database: Successfully cleared execution_tracking table. Removed 160 records.
[2025-07-03 06:24:54,615] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-03 06:24:54,626] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x113ae5fd0>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-03 06:24:54,626] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-07-03 06:24:54,654] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-07-03 06:24:54,683] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-07-03 06:24:56,689] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-07-03 06:24:56,690] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-07-03 06:24:57,548] INFO in appium_device_controller: Installed Appium drivers: 
[2025-07-03 06:24:57,548] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-07-03 06:24:58,332] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-07-03 06:24:58,332] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-07-03 06:24:58,332] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-07-03 06:24:58,340] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-07-03 06:25:00,357] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:00,358] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:02,369] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:02,369] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:04,379] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:04,379] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:06,385] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:06,385] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:08,391] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:08,391] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:10,399] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:10,399] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:12,405] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:12,405] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:14,413] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:14,413] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:16,422] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:16,422] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:18,426] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:18,426] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:20,432] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:20,432] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:22,438] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:22,438] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:24,443] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:24,443] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:26,450] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:26,451] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
[2025-07-03 06:25:28,456] INFO in appium_device_controller: Appium server started successfully
[2025-07-03 06:25:28,456] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '88d51d3345d5f62ae9770c346fa3dc261791bbfa', 'built': '2025-07-03 00:06:44 +1000'}}}
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-07-03 06:25:28,475] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://************:8080
[2025-07-03 06:25:28,475] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-07-03 06:26:37,585] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET / HTTP/1.1" 200 -
[2025-07-03 06:26:37,620] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,628] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /static/css/test-suites-styles.css HTTP/1.1" 200 -
[2025-07-03 06:26:37,629] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,630] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /static/css/test-cases-styles.css HTTP/1.1" 200 -
[2025-07-03 06:26:37,631] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,631] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,632] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,636] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,638] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,641] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,641] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,645] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,650] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,653] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,656] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /static/js/action-manager.js?v=1751490997 HTTP/1.1" 200 -
[2025-07-03 06:26:37,658] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,659] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,664] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,666] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,672] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,679] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,681] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,682] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,684] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,689] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,691] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,694] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,698] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,698] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /static/js/main.js?v=1751490997 HTTP/1.1" 200 -
[2025-07-03 06:26:37,703] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,704] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,723] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,752] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-03 06:26:37,755] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/environments HTTP/1.1" 200 -
[2025-07-03 06:26:37,763] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-03 06:26:37,767] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-07-03 06:26:37,773] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-03 06:26:37,774] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:26:37,782] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/settings HTTP/1.1" 200 -
[2025-07-03 06:26:37,788] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-07-03 06:26:37,800] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-03 06:26:37,809] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-03 06:26:37,817] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:26:37,819] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[33mGET /api/environments/current HTTP/1.1[0m" 404 -
[2025-07-03 06:26:37,823] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-07-03 06:26:37,829] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-03 06:26:37,836] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-03 06:26:37,840] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-07-03 06:26:37,848] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-03 06:26:37,889] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-07-03 06:26:37,914] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-03 06:26:37,938] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-03 06:26:37,955] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-07-03 06:26:37,971] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:37] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-03 06:26:38,802] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:38] "GET /api/devices HTTP/1.1" 200 -
[2025-07-03 06:26:42,748] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:26:42,749] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:26:42,751] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:26:42,752] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:26:46,571] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:46] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-03 06:26:46,595] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:46] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-03 06:26:47,747] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:26:47,748] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:26:47,751] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:26:47,752] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:26:52,749] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:26:52,751] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:26:52,754] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:26:52,755] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:26:57,749] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:26:57,750] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:26:57,753] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:26:57,754] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:26:59,664] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:59] "GET /api/healenium/config HTTP/1.1" 200 -
[2025-07-03 06:26:59,665] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:59] "GET /api/directory_paths HTTP/1.1" 200 -
[2025-07-03 06:26:59,667] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:59] "GET /api/settings HTTP/1.1" 200 -
[2025-07-03 06:26:59,670] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:26:59] "GET /api/settings HTTP/1.1" 200 -
[2025-07-03 06:27:02,749] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:27:02,751] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:27:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:27:02,754] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:27:02,755] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:27:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:27:07,747] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:27:07,748] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:27:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:27:07,750] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:27:07,751] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:27:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:27:12,748] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:27:12,750] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:27:12] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:27:12,753] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:27:12,754] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:27:12] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:27:15,281] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:27:15] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-03 06:27:15,305] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:27:15] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-03 06:27:17,748] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:27:17,750] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:27:17] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-03 06:27:17,753] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-03 06:27:17,754] INFO in _internal: 127.0.0.1 - - [03/Jul/2025 06:27:17] "GET /api/reports/latest HTTP/1.1" 200 -
