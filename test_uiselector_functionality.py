#!/usr/bin/env python3
"""
Test script for UISelector functionality in the mobile automation framework.
This script tests various UISelector patterns to ensure proper parsing and execution.
"""

import sys
import os
import json
import logging

# Add the app_android directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

from actions.android_functions_action import AndroidFunctionsAction

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockDriver:
    """Mock Appium driver that simulates failure"""

    def find_element(self, by, value):
        # Simulate Appium driver failure to test UIAutomator2 fallback
        raise Exception("Mock Appium driver failure - testing UIAutomator2 fallback")

class MockController:
    """Mock controller for testing UISelector parsing without actual device"""

    def __init__(self):
        self.driver = MockDriver()  # Provide a mock driver that will fail
        self.uiautomator2_helper = None
        
class MockUIAutomator2Helper:
    """Mock UIAutomator2Helper for testing"""
    
    def __init__(self):
        self.device_id = "test_device"
        
    def _run_adb_command(self, command):
        """Mock ADB command execution"""
        logger.debug(f"Mock ADB command: {command}")

        if 'uiautomator' in ' '.join(command) and 'dump' in ' '.join(command):
            logger.debug("Mock: UI dump command executed")
            return "UI dump successful"
        elif 'cat' in ' '.join(command) and 'ui_dump.xml' in ' '.join(command):
            logger.debug("Mock: Returning sample XML content")
            # Return sample XML for testing
            return '''<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>
<hierarchy rotation="0">
  <node index="0" text="Login" resource-id="com.example:id/login_button" class="android.widget.Button" package="com.example" content-desc="Login button" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[100,200][300,250]" />
  <node index="1" text="Sign Up" resource-id="com.example:id/signup_button" class="android.widget.Button" package="com.example" content-desc="Sign up button" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[100,300][300,350]" />
  <node index="2" text="" resource-id="com.example:id/username_field" class="android.widget.EditText" package="com.example" content-desc="Username input" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[50,100][350,150]" />
</hierarchy>'''

        logger.debug(f"Mock: No match for command, returning None")
        return None
        
    def tap_at_coordinates(self, x, y):
        """Mock tap at coordinates"""
        logger.info(f"Mock tap at coordinates: ({x}, {y})")
        return True

def test_uiselector_patterns():
    """Test various UISelector patterns"""
    
    # Create mock objects
    controller = MockController()
    controller.uiautomator2_helper = MockUIAutomator2Helper()
    
    # Create AndroidFunctionsAction instance
    action = AndroidFunctionsAction(controller)
    
    # Test cases with different UISelector patterns
    test_cases = [
        {
            "name": "Text exact match",
            "uiselector": 'new UiSelector().text("Login")',
            "expected_success": True,
            "expected_coords": (200, 225)  # Center of [100,200][300,250]
        },
        {
            "name": "Resource ID match",
            "uiselector": 'new UiSelector().resourceId("com.example:id/login_button")',
            "expected_success": True,
            "expected_coords": (200, 225)
        },
        {
            "name": "Class name match",
            "uiselector": 'new UiSelector().className("android.widget.Button")',
            "expected_success": True,
            "expected_coords": (200, 225)  # Should match first button
        },
        {
            "name": "Content description match",
            "uiselector": 'new UiSelector().description("Login button")',
            "expected_success": True,
            "expected_coords": (200, 225)
        },
        {
            "name": "Text contains match",
            "uiselector": 'new UiSelector().textContains("Log")',
            "expected_success": True,
            "expected_coords": (200, 225)
        },
        {
            "name": "Multiple criteria",
            "uiselector": 'new UiSelector().text("Login").className("android.widget.Button")',
            "expected_success": True,
            "expected_coords": (200, 225)
        },
        {
            "name": "Boolean attribute",
            "uiselector": 'new UiSelector().clickable(true)',
            "expected_success": True,
            "expected_coords": (200, 225)  # Should match first clickable element
        },
        {
            "name": "Non-existent element",
            "uiselector": 'new UiSelector().text("NonExistent")',
            "expected_success": False,
            "expected_coords": None
        },
        {
            "name": "Complex selector with quotes",
            "uiselector": 'new UiSelector().resourceId("com.example:id/username_field").className("android.widget.EditText")',
            "expected_success": True,
            "expected_coords": (200, 125)  # Center of [50,100][350,150]
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        logger.info(f"\n=== Testing: {test_case['name']} ===")
        logger.info(f"UISelector: {test_case['uiselector']}")
        
        try:
            # Execute the UISelector action
            result = action.execute({
                'function_name': 'tap_uiselector',
                'uiselector': test_case['uiselector']
            })
            
            success = result.get('status') == 'success'
            message = result.get('message', '')
            
            # Extract coordinates from success message if available
            coords = None
            if success and 'at (' in message:
                import re
                coord_match = re.search(r'at \((\d+), (\d+)\)', message)
                if coord_match:
                    coords = (int(coord_match.group(1)), int(coord_match.group(2)))
            
            test_result = {
                'name': test_case['name'],
                'uiselector': test_case['uiselector'],
                'expected_success': test_case['expected_success'],
                'actual_success': success,
                'expected_coords': test_case['expected_coords'],
                'actual_coords': coords,
                'message': message,
                'passed': success == test_case['expected_success']
            }
            
            # Additional validation for coordinates if both expected and actual are available
            if (test_result['passed'] and 
                test_case['expected_coords'] and 
                coords and 
                coords != test_case['expected_coords']):
                test_result['passed'] = False
                test_result['message'] += f" (Coordinate mismatch: expected {test_case['expected_coords']}, got {coords})"
            
            results.append(test_result)
            
            status = "PASS" if test_result['passed'] else "FAIL"
            logger.info(f"Result: {status}")
            logger.info(f"Message: {message}")
            if coords:
                logger.info(f"Coordinates: {coords}")
                
        except Exception as e:
            logger.error(f"Test failed with exception: {e}")
            results.append({
                'name': test_case['name'],
                'uiselector': test_case['uiselector'],
                'expected_success': test_case['expected_success'],
                'actual_success': False,
                'expected_coords': test_case['expected_coords'],
                'actual_coords': None,
                'message': f"Exception: {str(e)}",
                'passed': False
            })
    
    return results

def print_test_summary(results):
    """Print a summary of test results"""
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r['passed'])
    failed_tests = total_tests - passed_tests
    
    print(f"\n{'='*60}")
    print(f"UISelector Test Summary")
    print(f"{'='*60}")
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if failed_tests > 0:
        print(f"\nFailed Tests:")
        for result in results:
            if not result['passed']:
                print(f"  - {result['name']}: {result['message']}")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    print("Starting UISelector functionality tests...")
    
    try:
        results = test_uiselector_patterns()
        print_test_summary(results)
        
        # Save detailed results to file
        with open('uiselector_test_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\nDetailed results saved to: uiselector_test_results.json")
        
        # Exit with appropriate code
        failed_count = sum(1 for r in results if not r['passed'])
        sys.exit(0 if failed_count == 0 else 1)
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)
