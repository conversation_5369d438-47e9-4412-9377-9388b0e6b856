[{"name": "Text exact match", "uiselector": "new UiSelector().text(\"Login\")", "expected_success": true, "actual_success": true, "expected_coords": [200, 225], "actual_coords": [200, 225], "message": "Tapped element using UISelector via UIAutomator2: new UiSelector().text(\"Login\") at (200, 225)", "passed": true}, {"name": "Resource ID match", "uiselector": "new UiSelector().resourceId(\"com.example:id/login_button\")", "expected_success": true, "actual_success": true, "expected_coords": [200, 225], "actual_coords": [200, 225], "message": "Tapped element using UISelector via UIAutomator2: new UiSelector().resourceId(\"com.example:id/login_button\") at (200, 225)", "passed": true}, {"name": "Class name match", "uiselector": "new UiSelector().className(\"android.widget.Button\")", "expected_success": true, "actual_success": true, "expected_coords": [200, 225], "actual_coords": [200, 225], "message": "Tapped element using UISelector via UIAutomator2: new UiSelector().className(\"android.widget.Button\") at (200, 225)", "passed": true}, {"name": "Content description match", "uiselector": "new UiSelector().description(\"Login button\")", "expected_success": true, "actual_success": true, "expected_coords": [200, 225], "actual_coords": [200, 225], "message": "Tapped element using UISelector via UIAutomator2: new UiSelector().description(\"Login button\") at (200, 225)", "passed": true}, {"name": "Text contains match", "uiselector": "new UiSelector().textContains(\"Log\")", "expected_success": true, "actual_success": true, "expected_coords": [200, 225], "actual_coords": [200, 225], "message": "Tapped element using UISelector via UIAutomator2: new UiSelector().textContains(\"Log\") at (200, 225)", "passed": true}, {"name": "Multiple criteria", "uiselector": "new UiSelector().text(\"Login\").className(\"android.widget.Button\")", "expected_success": true, "actual_success": true, "expected_coords": [200, 225], "actual_coords": [200, 225], "message": "Tapped element using UISelector via UIAutomator2: new UiSelector().text(\"Login\").className(\"android.widget.Button\") at (200, 225)", "passed": true}, {"name": "Boolean attribute", "uiselector": "new UiSelector().clickable(true)", "expected_success": true, "actual_success": true, "expected_coords": [200, 225], "actual_coords": [200, 225], "message": "Tapped element using UISelector via UIAutomator2: new UiSelector().clickable(true) at (200, 225)", "passed": true}, {"name": "Non-existent element", "uiselector": "new UiSelector().text(\"NonExistent\")", "expected_success": false, "actual_success": false, "expected_coords": null, "actual_coords": null, "message": "Element not found with UISelector: new UiSelector().text(\"NonExistent\"). Available attributes in parsed selector: ['text']", "passed": true}, {"name": "Complex selector with quotes", "uiselector": "new UiSelector().resourceId(\"com.example:id/username_field\").className(\"android.widget.EditText\")", "expected_success": true, "actual_success": true, "expected_coords": [200, 125], "actual_coords": [200, 125], "message": "Tapped element using UISelector via UIAutomator2: new UiSelector().resourceId(\"com.example:id/username_field\").className(\"android.widget.EditText\") at (200, 125)", "passed": true}]